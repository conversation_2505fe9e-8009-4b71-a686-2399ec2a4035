<!--pages/people/people.wxml-->
<view class="list" style="padding-bottom:160rpx">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view wx:if="{{list.length>0}}">
		<view class="item bg-color-white m30" wx:for="{{list}}" wx:key="index" bindtap="choosePeople"
			data-id="{{item.inquirerId}}" data-index="{{index}}">
			<view class="isRz" wx:if="{{(item.idCard==='' || item.idCard===null ) && item.childTag===0}}"
				catchtap="peopleDetail" data-index="{{index}}" data-model="{{item.childTag}}">未实名<image
					src="/static/images/ic_more_white_01.png"></image>
			</view>
			<view class="label">{{item.relationName}}</view>
			<view class="name f32 b c333 pl30 pr30">
				{{item.name}}
				<text class="f32 c666 n ml30">{{item.gender===1?'男':item.gender==0?'女':''}}</text>
				<text class="f32 c666 n ml30" wx:if="{{item.age!==0 || item.age!=='' || item.age!==null}}">{{item.age}}</text>
			</view>
			<view class="c666 f28 lh40 mb10 pl30 pr30">
				<block wx:if="{{item.childTag==0}}">身份证：{{item.idCard?item.idCard:'-'}}</block>
				<block wx:if="{{item.childTag==1}}">监护人身份证：{{item.guardianIdCard}}</block>
			</view>
			<view class="c666 f28 lh40 pl30 pr30 pb35">
				<block wx:if="{{item.childTag==0}}">手机号：{{item.phone?item.phone:'-'}}</block>
				<block wx:if="{{item.childTag==1}}">监护人手机号：{{item.guardianPhone}}</block>
			</view>
			<view class="btn pr30">
				<view catchtap="del" data-index="{{index}}">
					<image src="{{imgObject.ic_address_deleted}}"></image>删除
				</view>
				<view catchtap="peopleDetail" data-index="{{index}}" data-model="{{item.childTag}}">
					<image src="{{imgObject.ic_address_edit}}"></image>编辑
				</view>
			</view>
		</view>
		<view class="tc f32 c999">已有{{list.length}}人，还能添加{{8-list.length}}人</view>
	</view>
	<view class="flex_line_c no_msg_box" wx:else>
		<image class="no_msg" src="{{imgObject.nomes}}"></image>
		<view class="f28 c666">您还没有就诊人</view>
	</view>
</view>

<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir">
	<button bindtap="addPeople" data-name="{{selfInfo.name}}" data-idcard="{{selfInfo.idCard}}"
		data-phone="{{selfInfo.phone}}">添加就诊人</button>
</view>