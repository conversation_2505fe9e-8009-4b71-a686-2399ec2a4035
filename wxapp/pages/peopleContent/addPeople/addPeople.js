// pages/addPeople/addPeople.js
const api = require('../../../config/api')
const util = require('../../../utils/util')
const check = require('../../../utils/check')
import { startEid } from '../../../mp_ecard_sdk/main'
Page({
  ...check,
  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '添加就诊人',
    imgObject: {
      ic_more_grey: api.ImgUrl + 'images/ic_more_grey.png'
    },
    type: null, //1.添加就诊人  2.添加用药人
    isDetail: false,
    relationList: [],
    relationListChild: [
      { gender: 0, relationId: 2, relationName: '子女' },
      { gender: 0, relationId: 4, relationName: '其他' }
    ],
    clickFlag: true,
    showPicker: false,
    doctorId: '',
    info: {
      childTag: 0, //0成人 1儿童
      name: '', //就诊人姓名
      idCard: '', //就诊人身份证号
      gender: 1, //性别    1男  0女
      phone: '', //就诊人手机号
      relation: 0, //关系默认选择
      allergy: '', //过敏史
      pastHistory: '', //既往史
      guardianName: '', //监护人姓名
      guardianIdCard: '', //监护人身份证号
      guardianPhone: '', //监护人手机号
      birthday: '', //出生日期
      maritalStatus: 0,
      receiverName: '', //收货人姓名
      receiverPhone: '' //收货人手机号
    },
    // 过敏史
    allergy: {
      checked: false,
      content: ''
    },
    // 既往史
    always: {
      checked: false,
      content: ''
    },
    currentDate: new Date().getTime(),
    maxDate: new Date().getTime(),
    minDate: new Date(new Date().getFullYear() - 15, new Date().getMonth(), new Date().getDate() + 1).getTime(),
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },
    idCardClick: true,
    eidToken: ''
  },
  // 儿童和成人切换
  switchPeople() {
    const childTag = this.data.info.childTag
    this.setData({
      ['info.childTag']: childTag === 0 ? 1 : 0,
      ['info.relation']: childTag === 0 ? 2 : 0
    })
  },
  // 就诊人性别切换
  radioChange(e) {
    this.setData({
      ['info.gender']: e.detail.value
    })
  },
  // 接受子组件传递过来的选择结果
  propContent: function(event) {
    var data = event.detail.data
    this.setData({
      allergy: data.allergy,
      always: data.always,
      ['info.allergy']: data.allergy.checked ? data.allergy.content : '',
      ['info.pastHistory']: data.always.checked ? data.always.content : ''
    })
  },
  // 身份证号同步
  idCardChange(e) {
    let birthday = ''
    if (e.detail.value.length == 18 && !this.isValidIdCard(e.detail.value)) {
      util.showToast({ 'title': '就诊人身份证号码格式不正确' })
      return
    }
    if (e.detail.value.length == 18 || e.detail.value.length == 0) {
      if (this.isValidIdCard(e.detail.value)) {
        birthday = util.IdCard(e.detail.value, 1)
      }
      this.setData({
        ['info.birthday']: birthday,
        idCardClick: birthday ? false : true
      })
    }
    this.setData({
      ['info.idCard']: e.detail.value
    })
  },
  // 文本框内容同步
  inputChange(e) {
    var type = e.currentTarget.dataset.type
    var value = 'info' + '.' + type
    var inputValue = util.filterEmoji(e.detail.value)

    this.setData({
      [value]: inputValue
    })

    // 自动填充收货人信息
    if (type === 'name') {
      // 如果填写就诊人姓名，则自动带入收货人姓名
      this.setData({
        'info.receiverName': inputValue
      })
    } else if (type === 'phone') {
      // 如果填写就诊人手机号，则自动带入收货人手机号
      this.setData({
        'info.receiverPhone': inputValue
      })
    }
  },
  inputChangeName(e) {
    this.setData({
      ['info.guardianName']: e.detail.value
    })
  },
  inputChangePhone(e) {
    this.setData({
      ['info.guardianPhone']: e.detail.value
    })
  },
  inputChangeIdCard(e) {
    this.setData({
      ['info.guardianIdCard']: e.detail.value
    })
  },
  // 与就诊人关系
  relationChange(e) {
    this.setData({
      ['info.relation']: e.currentTarget.dataset.id
    })
  },
  // 选择出生日期弹窗
  showAddress(e) {
    // util.hideKeyboard()
    if (!this.data.idCardClick) {
      return
    }
    this.setData({
      showPicker: !this.data.showPicker
    })
  },
  onInput(e) {
    this.setData({
      showPicker: !this.data.showPicker,
      ['info.birthday']: util.getTime(e.detail)
    })
  },
  onCancel(e) {
    this.setData({
      showPicker: !this.data.showPicker
    })
  },
  // 保存方法
  save() {
    var username = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{1,10}$/
    if (this.data.info.name.length < 2 || this.data.info.name.length > 6 || !username.test(this.data.info.name)) {
      util.showToast({ 'title': '请输入正确的就诊人姓名' })
      return
    }
    if (!this.isValidNull(this.data.info.name)) {
      util.showToast({ 'title': '请输入就诊人姓名' })
    } else
    if (this.data.info.childTag === 0) {
      if (!this.isValidNull(this.data.info.idCard)) {
        util.showToast({ 'title': '请输入就诊人身份证号' })
      } else if (!this.isValidIdCard(this.data.info.idCard)) {
        util.showToast({ 'title': '就诊人身份证号码格式不正确' })
      } else if (!this.isValidNull(this.data.info.phone)) {
        util.showToast({ 'title': '请输入就诊人手机号' })
      } else if (!this.isValidPhone(this.data.info.phone)) {
        util.showToast({ 'title': '就诊人手机号格式不正确' })
      } else if (!this.data.info.cityId) {
        util.showToast({ 'title': '请选择当前地址' })
      } else if (!this.data.info.address) {
        util.showToast({ 'title': '请输入详细地址' })
      } else if (!this.data.info.contactName) {
        util.showToast({ 'title': '请填写紧急联系人姓名' })
      } else if (!this.data.info.contactPhone) {
        util.showToast({ 'title': '请填写紧急联系人手机号' })
      } else if (!this.isValidPhone(this.data.info.contactPhone)) {
        util.showToast({ 'title': '请填写正确紧急联系人手机号' })
      } else {
        this.addPeople()
      }
    } else {
      if (this.data.info.idCard && !this.isValidIdCard(this.data.info.idCard)) {
        util.showToast({ 'title': '就诊人身份证号码格式不正确' })
        return
      }
      if (!this.isValidNull(this.data.info.birthday)) {
        util.showToast({ 'title': '请输入就诊人出生日期' })
      } else if (this.data.info.guardianName.length < 2) {
        util.showToast({ 'title': '请输入正确的紧急联系人姓名' })
      } else if (!this.isValidNull(this.data.info.guardianName)) {
        util.showToast({ 'title': '请输入紧急联系人姓名' })
      } else if (!this.isValidNull(this.data.info.guardianPhone)) {
        util.showToast({ 'title': '请输入紧急联系人手机号' })
      } else if (!this.isValidPhone(this.data.info.guardianPhone)) {
        util.showToast({ 'title': '紧急联系人手机号格式不正确' })
      } else if (!this.data.info.cityId) {
        util.showToast({ 'title': '请选择当前地址' })
      } else if (!this.data.info.address) {
        util.showToast({ 'title': '请输入详细地址' })
      } else {
        this.addPeople()
      }
    }
  },
  // 增加就诊人
  addPeople() {
    util.showLoading({
      title: '提交中',
      icon: 'loading'
    })
    const flag = this.data.clickFlag
    if (!flag) {
      return false
    }
    this.setData({
      flag: false
    })

    // 准备保存参数
    const saveParams = { ...this.data.info }
    saveParams.contactName = this.data.info.guardianName
    saveParams.contactPhone = this.data.info.guardianPhone

    // 添加视频咨询相关参数
    if (this.data.holderId) {
      saveParams.holderId = this.data.holderId
    }
    if (this.data.subOrderCode) {
      saveParams.subOrderCode = this.data.subOrderCode
    }
    if (this.data.packageCode) {
      saveParams.packageCode = this.data.packageCode
    }

    console.log('新增患者保存参数:', {
      name: saveParams.name,
      holderId: saveParams.holderId,
      subOrderCode: saveParams.subOrderCode,
      packageCode: saveParams.packageCode
    })

    util.request(api.addPeople, saveParams, 'post', 2)
      .then(res => {
        util.hideLoading()
        if (res.data.code === 0) {
          const { eidToken, faceFlag } = res.data.data
          if (faceFlag) {
            this.data.eidToken = eidToken
            this.goStartEid()
          } else {
            util.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 1000
            })

            // 如果是从视频咨询页面跳转过来的，设置全局标识以便返回时刷新数据
            if (this.data.holderId || this.data.subOrderCode || this.data.packageCode) {
              const app = getApp()
              if (app.globalData) {
                app.globalData.needRefreshVideoConsultPatientList = true
                console.log('新增患者成功，设置视频咨询页面刷新标识')
              }
            }

            setTimeout(() => {
              wx.navigateBack({
                delta: 1
              })
            }, 1000)
          }
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
        this.setData({
          clickFlag: true
        })
      })
      .catch(res => {
        console.error('新增患者保存失败:', res)
        this.setData({
          clickFlag: true
        })
      })
  },
  // 获取关系字典表
  getRelation() {
    util.request(api.relationList, {}, 'post')
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            relationList: res.data.data.relationList
          })
        }
      })
      .catch(res => {
      })
  },
  onMaritalChange(e) {
    this.setData({
      'info.maritalStatus': e.detail.value
    })
  },
  onPickerConfim(options) {
    const { areaId } = options.detail
    this.setData({
      'info.cityId': areaId[2]
    })
    console.log(options, 'onPickerConfim')
  },
  onInputVal(e) {
    const val = e.detail
    const { key } = e.currentTarget.dataset
    this.setData({
      [`info.${key}`]: util.filterEmoji(val)
    })
    console.log(this.data.info)
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('新增患者页面参数:', options)

    // 基础参数设置
    const updateData = {
      type: options.type * 1,
      doctorId: options.doctorId,
      source: options.source * 1
    }

    // 处理传统的紧急联系人参数（非视频咨询场景）
    if (options.guardianName || (options.name && !options.holderId && !options.subOrderCode)) {
      // 如果有明确的 guardianName 参数，或者是传统的 name 参数（非视频咨询场景）
      updateData['info.guardianName'] = options.guardianName || options.name || ''
      updateData['info.contactName'] = options.guardianName || options.name || ''
    } else {
      // 视频咨询场景或其他场景，不自动填充紧急联系人
      updateData['info.guardianName'] = ''
      updateData['info.contactName'] = ''
    }

    // 处理其他传统参数
    updateData['info.guardianIdCard'] = options.idcard || ''
    updateData['info.guardianPhone'] = options.phone || ''
    updateData['info.contactPhone'] = options.phone || ''

    // 如果是从视频咨询页面跳转过来，自动填充患者信息（不是紧急联系人）
    if (options.holderId || options.subOrderCode || options.packageCode) {
      // 自动填充患者姓名
      if (options.name && options.name !== '未知') {
        try {
          // 先检查是否需要解码
          let patientName = options.name
          if (patientName.includes('%')) {
            // 包含编码字符，需要解码
            patientName = decodeURIComponent(patientName)
          }
          updateData['info.name'] = patientName
          console.log('自动填充患者姓名:', patientName, '(原始值:', options.name, ')')
        } catch (error) {
          console.error('解码患者姓名失败:', error, '原始值:', options.name)
          updateData['info.name'] = options.name // 如果解码失败，直接使用原始值
          console.log('使用原始患者姓名:', options.name)
        }
      }

      // 自动填充患者手机号
      if (options.phone && options.phone.trim() !== '') {
        try {
          // 先检查是否需要解码
          let patientPhone = options.phone
          if (patientPhone.includes('%')) {
            // 包含编码字符，需要解码
            patientPhone = decodeURIComponent(patientPhone)
          }
          updateData['info.phone'] = patientPhone
          console.log('自动填充患者手机号:', patientPhone, '(原始值:', options.phone, ')')
        } catch (error) {
          console.error('解码患者手机号失败:', error, '原始值:', options.phone)
          updateData['info.phone'] = options.phone // 如果解码失败，直接使用原始值
          console.log('使用原始患者手机号:', options.phone)
        }
      }
    }

    // 添加视频咨询相关参数存储
    if (options.holderId) {
      updateData.holderId = options.holderId
      console.log('接收到 holderId 参数:', options.holderId)
    }
    if (options.subOrderCode) {
      updateData.subOrderCode = options.subOrderCode
      console.log('接收到 subOrderCode 参数:', options.subOrderCode)
    }
    if (options.packageCode) {
      updateData.packageCode = options.packageCode
      console.log('接收到 packageCode 参数:', options.packageCode)
    }
    if (options.consultId) {
      updateData.consultId = options.consultId
      console.log('接收到 consultId 参数:', options.consultId)
    }

    this.setData(updateData, () => {
      // 页面数据设置完成后，立即自动填充收货人信息
      this.autoFillReceiverInfo()
    })
    console.log('新增患者页面初始化完成，患者信息:', this.data.info)
    console.log('紧急联系人信息:', {
      guardianName: this.data.info.guardianName,
      contactName: this.data.info.contactName
    })
    this.getRelation()
  },

  /**
   * 自动填充收货人信息
   * 在页面加载完成后立即执行，无需等待用户输入
   */
  autoFillReceiverInfo() {
    const patientName = this.data.info.name
    const patientPhone = this.data.info.phone

    const updateData = {}
    let hasUpdate = false

    // 自动填充收货人姓名
    if (patientName && patientName.trim() !== '' && patientName !== '未知') {
      if (!this.data.info.receiverName || this.data.info.receiverName.trim() === '') {
        updateData['info.receiverName'] = patientName.trim()
        hasUpdate = true
        console.log('新增页面自动填充收货人姓名:', patientName)
      }
    }

    // 自动填充收货人手机号
    if (patientPhone && patientPhone.trim() !== '') {
      if (!this.data.info.receiverPhone || this.data.info.receiverPhone.trim() === '') {
        updateData['info.receiverPhone'] = patientPhone.trim()
        hasUpdate = true
        console.log('新增页面自动填充收货人手机号:', patientPhone)
      }
    }

    // 如果有需要更新的数据，则更新页面
    if (hasUpdate) {
      this.setData(updateData, () => {
        console.log('新增页面收货人信息自动填充完成:', {
          receiverName: this.data.info.receiverName,
          receiverPhone: this.data.info.receiverPhone
        })
      })
    } else {
      console.log('新增页面无需自动填充收货人信息，当前状态:', {
        patientName: patientName,
        patientPhone: patientPhone,
        receiverName: this.data.info.receiverName,
        receiverPhone: this.data.info.receiverPhone
      })
    }
  },

  // 人脸核身
  async goStartEid() {
    const that = this
    startEid({
      data: {
        token: that.data.eidToken
      },
      verifyDoneCallback(res) {
        const { token, verifyDone } = res
        console.log('收到核身完成的res:', res)
        console.log('核身的token是:', token)
        console.log('是否完成核身:', verifyDone)
        if (verifyDone) {
          that.inquirerFace(that.data.eidToken)
        }
      }
    })
  },
  async inquirerFace(eidToken) {
    try {
      const { data } = await util.request(`${api.inquirerFace}?eidToken=${eidToken}`, {
        eidToken
      }, 'post')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
        return
      }

      // 如果是从视频咨询页面跳转过来的，设置全局标识以便返回时刷新数据
      if (this.data.holderId || this.data.subOrderCode || this.data.packageCode) {
        const app = getApp()
        if (app.globalData) {
          app.globalData.needRefreshVideoConsultPatientList = true
          console.log('新增患者人脸核身成功，设置视频咨询页面刷新标识')
        }
      }

      wx.navigateBack({
        delta: 1
      })
      console.log(eidToken, 'eidToken', data, '==================================inquirerFace==================================')
    } catch (error) {
      throw new Error(error)
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
